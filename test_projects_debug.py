#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تشخيص مشكلة قسم المشاريع
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_projects_class():
    """اختبار كلاس المشاريع"""
    try:
        from database import get_session
        from ui.projects import ProjectsWidget
        
        # إنشاء جلسة قاعدة البيانات
        session = get_session()
        
        # التحقق من وجود الدالة في الكلاس
        print("🔍 فحص كلاس ProjectsWidget:")
        print(f"✅ الكلاس موجود: {ProjectsWidget}")
        
        # التحقق من وجود الدالة
        if hasattr(ProjectsWidget, 'create_custom_status_filter'):
            print("✅ الدالة create_custom_status_filter موجودة في الكلاس")
        else:
            print("❌ الدالة create_custom_status_filter غير موجودة في الكلاس")
            
        # محاولة إنشاء مثيل
        print("\n🔧 محاولة إنشاء مثيل من ProjectsWidget:")
        try:
            projects_widget = ProjectsWidget(session)
            print("✅ تم إنشاء المثيل بنجاح")
            
            # التحقق من وجود الدالة في المثيل
            if hasattr(projects_widget, 'create_custom_status_filter'):
                print("✅ الدالة موجودة في المثيل")
            else:
                print("❌ الدالة غير موجودة في المثيل")
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء المثيل: {e}")
            import traceback
            traceback.print_exc()
            
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_projects_class()
