#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Migration: إضافة property_id إلى جدول documents
تاريخ الإنشاء: 2024-12-19
الهدف: إضافة دعم ربط الوثائق بالعقارات في جدول Document العام
"""

import sqlite3
import os
import sys

def add_property_id_column():
    """إضافة عمود property_id إلى جدول documents"""
    
    # مسار قاعدة البيانات
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'accounting.db')
    
    if not os.path.exists(db_path):
        print("❌ لم يتم العثور على قاعدة البيانات")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود العمود
        cursor.execute("PRAGMA table_info(documents)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'property_id' in columns:
            print("✅ العمود property_id موجود بالفعل في جدول documents")
            conn.close()
            return True
        
        print("🔄 إضافة عمود property_id إلى جدول documents...")
        
        # إضافة العمود الجديد
        cursor.execute("""
            ALTER TABLE documents 
            ADD COLUMN property_id INTEGER 
            REFERENCES properties(id)
        """)
        
        # إنشاء فهرس على العمود الجديد
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_documents_property_id 
            ON documents(property_id)
        """)
        
        # إنشاء فهرس مركب للعقار والنوع
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_document_property_type 
            ON documents(property_id, file_type)
        """)
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print("✅ تم إضافة عمود property_id بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة العمود: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def migrate_property_documents():
    """نقل البيانات من PropertyDocument إلى Document"""
    
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'accounting.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود جدول property_documents
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='property_documents'
        """)
        
        if not cursor.fetchone():
            print("ℹ️ جدول property_documents غير موجود - لا حاجة للنقل")
            conn.close()
            return True
        
        print("🔄 نقل البيانات من property_documents إلى documents...")
        
        # نقل البيانات
        cursor.execute("""
            INSERT INTO documents (title, file_path, file_type, upload_date, description, property_id)
            SELECT title, file_path, file_type, upload_date, description, property_id
            FROM property_documents
            WHERE property_id NOT IN (
                SELECT DISTINCT property_id 
                FROM documents 
                WHERE property_id IS NOT NULL
            )
        """)
        
        rows_migrated = cursor.rowcount
        
        conn.commit()
        conn.close()
        
        print(f"✅ تم نقل {rows_migrated} وثيقة من property_documents إلى documents")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في نقل البيانات: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def main():
    """تشغيل Migration"""
    print("🚀 بدء Migration: إضافة property_id إلى جدول documents")
    print("=" * 60)
    
    # الخطوة 1: إضافة العمود
    if not add_property_id_column():
        print("❌ فشل في إضافة العمود")
        sys.exit(1)
    
    # الخطوة 2: نقل البيانات
    if not migrate_property_documents():
        print("❌ فشل في نقل البيانات")
        sys.exit(1)
    
    print("=" * 60)
    print("✅ تم إكمال Migration بنجاح!")
    print("📝 الآن يمكن للعقارات استخدام جدول Document العام")

if __name__ == "__main__":
    main()
