"""
لوحة المعلومات المتطورة النظيفة - بدون أكواد غير مستخدمة
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QFrame, QProgressBar, QScrollArea, QSizePolicy)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont
from ui.unified_styles import UnifiedStyles
import datetime


class CleanAdvancedDashboard(QWidget):
    """لوحة المعلومات المتطورة النظيفة"""

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        # التخطيط الرئيسي - استغلال كامل المساحة
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                background: rgba(0, 0, 0, 0.1);
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: rgba(0, 0, 0, 0.3);
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: rgba(0, 0, 0, 0.5);
            }
        """)

        # الحاوية الرئيسية للمحتوى - استغلال كامل مع حفظ المكونات
        content_widget = QWidget()
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش لاستغلال الفراغ
        content_layout.setSpacing(15)  # الحفاظ على المسافات بين المكونات

        # تطبيق التصميم الأنيق
        self.apply_elegant_styling()

        # إنشاء الرسم البياني المتطور الوحيد الشامل
        self.create_ultra_advanced_single_chart(content_layout)

        content_widget.setLayout(content_layout)
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)
        self.setLayout(main_layout)

        # بدء تحديث التاريخ وآخر تحديث
        self.update_datetime()
        self.update_last_update_time()

        # مؤقت التاريخ (تحديث أبطأ لتجنب الوميض)
        self.date_timer = QTimer()
        self.date_timer.timeout.connect(self.update_datetime)
        self.date_timer.start(300000)  # تحديث كل 5 دقائق

        # مؤقت آخر تحديث (تحديث أبطأ لتجنب الوميض)
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_last_update_time)
        self.update_timer.start(120000)  # تحديث كل دقيقتين

        print("✅ تم إنشاء الواجهة المتطورة النظيفة بنجاح!")

    def update_datetime(self):
        """تحديث التاريخ فقط مع معلومات إضافية"""
        now = datetime.datetime.now()

        # تنسيق التاريخ: سنة/شهر/يوم
        date_str = now.strftime("%Y/%m/%d")

        # أسماء الأيام بالعربية
        arabic_days = ["الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت", "الأحد"]
        day_name = arabic_days[now.weekday()]

        # تحديد الأيقونة حسب اليوم
        day_icons = {
            "الاثنين": "🌟",
            "الثلاثاء": "💫",
            "الأربعاء": "⭐",
            "الخميس": "✨",
            "الجمعة": "🌙",
            "السبت": "☀️",
            "الأحد": "🌅"
        }
        day_icon = day_icons.get(day_name, "📅")

        # عرض التاريخ واليوم فقط (بدون وقت)
        datetime_str = f"{day_icon} {day_name} | 📅 {date_str}"
        self.datetime_label.setText(datetime_str)

    def update_last_update_time(self):
        """تحديث مؤشر آخر تحديث المتطور بدون وقت"""
        now = datetime.datetime.now()

        # تحديد الأيقونة والرسالة حسب الوقت
        hour = now.hour
        if 6 <= hour < 12:
            update_icon = "🌟"
            update_message = "تحديث صباحي"
            status_color = "rgba(255, 193, 7, 0.8)"
        elif 12 <= hour < 18:
            update_icon = "⚡"
            update_message = "تحديث نهاري"
            status_color = "rgba(255, 152, 0, 0.8)"
        elif 18 <= hour < 22:
            update_icon = "🔥"
            update_message = "تحديث مسائي"
            status_color = "rgba(156, 39, 176, 0.8)"
        else:
            update_icon = "💫"
            update_message = "تحديث ليلي"
            status_color = "rgba(63, 81, 181, 0.8)"

        # رسائل متنوعة للتحديث بأيقونات محسنة
        update_messages = [
            f"{update_icon} {update_message} - نشط",
            f"🔄 {update_message} - محدث",
            f"⚡ {update_message} - مباشر",
            f"📈 {update_message} - فعال",
            f"🎯 {update_message} - متاح",
            f"✨ {update_message} - جاهز",
            f"🚀 {update_message} - متطور",
            f"💫 {update_message} - ذكي"
        ]

        # اختيار رسالة عشوائية
        import random
        selected_message = random.choice(update_messages)
        self.last_update_label.setText(selected_message)

        # تحديث لون الخلفية حسب الوقت بدون إطارات - شفاف
        self.last_update_label.setStyleSheet("""
            color: white;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            border: none;
            padding: 8px 16px;
        """)

    def create_ultra_advanced_single_chart(self, layout):
        """إنشاء رسم بياني واحد متطور جداً يحتوي على كل شيء"""
        # الحاوية الرئيسية للرسم البياني المتطور
        main_chart_frame = QFrame()
        main_chart_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #475569, stop:0.8 #334155,
                    stop:0.9 #1E293B, stop:1 #0F172A);
                border: none;
                border-radius: 20px;
                margin: 2px;
            }
        """)

        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش لاستغلال الفراغ
        main_layout.setSpacing(20)  # الحفاظ على المسافات بين الأقسام

        # العنوان الرئيسي المتطور مع ألوان متدرجة بدون إطارات
        title_container = QWidget()
        title_container.setFixedHeight(80)  # تحديد ارتفاع 80 للمساحة الرئيسية
        title_container.setStyleSheet("""
            QWidget {
                background: transparent;
                border: none;
                margin: 2px;
            }
        """)

        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(8, 6, 8, 6)  # هوامش مناسبة للارتفاع 80
        title_layout.setSpacing(8)  # مسافات مناسبة بين العناصر

        # العنوان المتطور مع أفضل أيقونة للوحة التحكم
        main_title = QLabel("⚡ لوحة التحكم الذكية المتطورة - تحليلات شاملة")
        main_title.setFont(QFont("Arial", 24, QFont.Bold))
        main_title.setStyleSheet("""
            color: white;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            border: none;
            padding: 8px 16px;
        """)
        title_layout.addWidget(main_title)

        title_layout.addStretch()

        # التاريخ المتطور بدون إطارات
        self.datetime_label = QLabel()
        self.datetime_label.setFont(QFont("Arial", 24, QFont.Bold))
        self.datetime_label.setStyleSheet("""
            color: white;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(255, 255, 255, 0.2),
                stop:1 rgba(255, 255, 255, 0.1));
            border-radius: 15px;
            border: none;
            padding: 8px 16px;
        """)
        self.datetime_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(self.datetime_label)

        title_layout.addStretch()

        # مؤشر آخر تحديث المتطور بدون إطارات
        self.last_update_label = QLabel()
        self.last_update_label.setFont(QFont("Arial", 24, QFont.Bold))
        self.last_update_label.setStyleSheet("""
            color: white;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            border: none;
            padding: 8px 16px;
        """)
        self.update_last_update_time()
        title_layout.addWidget(self.last_update_label)

        title_container.setLayout(title_layout)
        main_layout.addWidget(title_container)

        # إضافة مساحة مرنة في الأعلى لتوسيط المحتوى
        main_layout.addStretch(1)

        # القسم الأول: الإحصائيات الرئيسية المتطورة (في المنتصف)
        stats_section = self.create_ultra_stats_section()
        main_layout.addWidget(stats_section)

        # إضافة مساحة صغيرة بين الإحصائيات والرسوم البيانية
        main_layout.addSpacing(5)

        # القسم الثاني: الرسوم البيانية التفاعلية المتطورة (في المنتصف)
        charts_section = self.create_ultra_charts_section()
        main_layout.addWidget(charts_section)

        # إضافة مساحة مرنة في الأسفل لتوسيط المحتوى
        main_layout.addStretch(1)

        main_chart_frame.setLayout(main_layout)
        layout.addWidget(main_chart_frame)

    def create_ultra_stats_section(self):
        """إنشاء قسم الإحصائيات المتطور بدون إطارات"""
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background: transparent;
                border: none;
            }
        """)

        stats_layout = QVBoxLayout()
        stats_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش لاستغلال الفراغ
        stats_layout.setSpacing(15)  # الحفاظ على المسافات بين المكونات

        # عنوان القسم المتطور مع أيقونة محسنة
        section_title = QLabel("⚡ الإحصائيات الذكية والتحليلات المتقدمة")
        section_title.setFont(QFont("Arial", 20, QFont.Bold))
        section_title.setStyleSheet("""
            color: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #8B5CF6,
                stop:0.5 #EC4899,
                stop:1 #F59E0B);
            background: transparent;
            padding: 10px;
        """)
        section_title.setAlignment(Qt.AlignCenter)
        stats_layout.addWidget(section_title)

        # شبكة الإحصائيات - مسافات مضبوطة
        stats_grid = QHBoxLayout()
        stats_grid.setSpacing(10)  # مسافات مضبوطة بين البطاقات

        # إحصائيات متطورة مع أيقونات وعناوين محسنة
        ultra_stats = [
            ("💎", "إجمالي الإيرادات المتميزة", "2,450,000 جنيه", "#27ae60", "+15.2%"),
            ("🚀", "قاعدة العملاء النشطة", "1,247 عميل", "#3498db", "+8.7%"),
            ("🔥", "المشاريع الحيوية", "89 مشروع", "#e74c3c", "+12.4%"),
            ("⚡", "معدل الإنجاز السريع", "94.2%", "#9b59b6", "+2.1%"),
            ("🌟", "التقييم الاستثنائي", "ممتاز", "#f39c12", "+5.8%")
        ]

        for icon, title, value, color, growth in ultra_stats:
            stat_card = self.create_ultra_stat_card(icon, title, value, color, growth)
            stats_grid.addWidget(stat_card)

        stats_layout.addLayout(stats_grid)
        stats_frame.setLayout(stats_layout)
        return stats_frame

    def create_ultra_stat_card(self, icon, title, value, color, growth):
        """إنشاء بطاقة إحصائية متطورة بدون إطارات مع ارتفاع موحد"""
        card = QFrame()
        card.setFixedHeight(200)  # زيادة الارتفاع أكثر لتكبير الطول
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.98),
                    stop:0.15 rgba(248, 250, 252, 0.96),
                    stop:0.3 rgba(241, 245, 249, 0.94),
                    stop:0.45 rgba(226, 232, 240, 0.92),
                    stop:0.6 rgba(203, 213, 225, 0.9),
                    stop:0.75 rgba(148, 163, 184, 0.88),
                    stop:0.9 rgba(100, 116, 139, 0.86),
                    stop:1 rgba(71, 85, 105, 0.84));
                border: none;
                border-radius: 28px;
                margin: 2px;
                box-shadow: 0 20px 40px rgba(15, 23, 42, 0.06),
                           0 12px 25px rgba(37, 99, 235, 0.04),
                           0 6px 15px rgba(59, 130, 246, 0.03),
                           0 3px 8px rgba(96, 165, 250, 0.02),
                           inset 0 3px 6px rgba(255, 255, 255, 0.95),
                           inset 0 -2px 4px rgba(148, 163, 184, 0.08);
                transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            }}
            QFrame:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 1.0),
                    stop:0.15 rgba(255, 255, 255, 1.0),
                    stop:0.3 rgba(255, 255, 255, 1.0),
                    stop:0.45 rgba(255, 255, 255, 1.0),
                    stop:0.6 rgba(255, 255, 255, 1.0),
                    stop:0.75 rgba(248, 250, 252, 0.98),
                    stop:0.9 rgba(241, 245, 249, 0.96),
                    stop:1 rgba(226, 232, 240, 0.94));
                border: none;
                transform: translateY(-10px) scale(1.04) rotateX(2deg);
                box-shadow: 0 35px 60px rgba(15, 23, 42, 0.15),
                           0 20px 40px rgba(37, 99, 235, 0.12),
                           0 12px 25px rgba(59, 130, 246, 0.1),
                           0 6px 15px rgba(96, 165, 250, 0.08),
                           inset 0 4px 8px rgba(255, 255, 255, 1.0),
                           inset 0 -3px 6px rgba(148, 163, 184, 0.12);
            }}
            QFrame:active {{
                transform: translateY(-4px) scale(1.02);
                transition: all 0.15s cubic-bezier(0.4, 0, 0.6, 1);
            }}
        """)

        card_layout = QVBoxLayout()
        card_layout.setContentsMargins(8, 10, 8, 12)  # هوامش كافية مع زيادة الهامش السفلي
        card_layout.setSpacing(4)  # مسافات مضغوطة لاستغلال المساحة

        # الأيقونة مع حل جذري نهائي لمشكلة القطع
        icon_label = QLabel(icon)
        icon_label.setFont(QFont("Segoe UI", 30, QFont.Bold))
        icon_label.setAlignment(Qt.AlignCenter)

        icon_label.setStyleSheet(f"""
            color: {color};
            background: qradialgradient(cx:0.5, cy:0.5, radius:0.9,
                stop:0 rgba(255, 255, 255, 0.4),
                stop:0.3 rgba(255, 255, 255, 0.25),
                stop:0.6 rgba(255, 255, 255, 0.15),
                stop:0.8 rgba(255, 255, 255, 0.08),
                stop:1 transparent);
            font-weight: bold;
            text-shadow: 3px 3px 8px rgba(0, 0, 0, 0.3),
                        2px 2px 4px rgba(0, 0, 0, 0.2),
                        1px 1px 3px rgba(255, 255, 255, 0.9),
                        0px 0px 2px rgba(255, 255, 255, 0.7);
            border-radius: 16px;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1),
                       inset 0 2px 4px rgba(255, 255, 255, 0.6);
        """)
        icon_label.setFixedHeight(45)
        icon_label.setScaledContents(False)
        icon_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        card_layout.addWidget(icon_label)

        # القيمة الرئيسية مع حجم أكبر لاستغلال المساحة
        value_label = QLabel(value)
        value_label.setFont(QFont("Segoe UI", 20, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"""
            color: {color};
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(255, 255, 255, 0.3),
                stop:0.3 rgba(255, 255, 255, 0.2),
                stop:0.6 rgba(255, 255, 255, 0.15),
                stop:1 rgba(255, 255, 255, 0.08));
            font-weight: bold;
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.25),
                        2px 2px 4px rgba(0, 0, 0, 0.15),
                        1px 1px 3px rgba(255, 255, 255, 0.9),
                        0px 0px 2px rgba(255, 255, 255, 0.7);
            letter-spacing: 1px;
            border-radius: 14px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08),
                       inset 0 1px 3px rgba(255, 255, 255, 0.7);
        """)
        value_label.setFixedHeight(44)
        value_label.setScaledContents(False)
        value_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        card_layout.addWidget(value_label)

        # العنوان مع حجم أكبر لاستغلال المساحة
        title_label = QLabel(title)
        title_label.setFont(QFont("Segoe UI", 13, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            color: #0f172a;
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(255, 255, 255, 0.25),
                stop:0.3 rgba(255, 255, 255, 0.18),
                stop:0.6 rgba(255, 255, 255, 0.12),
                stop:1 rgba(255, 255, 255, 0.08));
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(255, 255, 255, 0.9),
                        1px 1px 3px rgba(255, 255, 255, 0.8),
                        0px 0px 2px rgba(0, 0, 0, 0.1);
            letter-spacing: 0.5px;
            border-radius: 12px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06),
                       inset 0 1px 2px rgba(255, 255, 255, 0.8);
        """)
        title_label.setWordWrap(True)
        title_label.setFixedHeight(42)
        title_label.setScaledContents(False)
        title_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        card_layout.addWidget(title_label)

        # مؤشر النمو مع حجم أكبر لاستغلال المساحة
        growth_label = QLabel(f"📈 {growth}")
        growth_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        growth_label.setAlignment(Qt.AlignCenter)
        growth_label.setStyleSheet("""
            color: #059669;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(5, 150, 105, 0.3),
                stop:0.2 rgba(16, 185, 129, 0.25),
                stop:0.4 rgba(34, 197, 94, 0.2),
                stop:0.6 rgba(52, 211, 153, 0.15),
                stop:0.8 rgba(74, 222, 128, 0.12),
                stop:1 rgba(134, 239, 172, 0.1));
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(255, 255, 255, 0.9),
                        1px 1px 3px rgba(255, 255, 255, 0.7),
                        0px 0px 2px rgba(5, 150, 105, 0.4);
            border-radius: 14px;
            letter-spacing: 0.4px;
            box-shadow: 0 2px 6px rgba(5, 150, 105, 0.25),
                       0 1px 3px rgba(5, 150, 105, 0.15),
                       inset 0 2px 4px rgba(255, 255, 255, 0.8),
                       inset 0 -1px 2px rgba(5, 150, 105, 0.1);
        """)
        growth_label.setFixedHeight(32)
        growth_label.setScaledContents(False)
        growth_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        card_layout.addWidget(growth_label)

        card.setLayout(card_layout)
        return card

    def create_ultra_charts_section(self):
        """إنشاء قسم الرسوم البيانية المتطور بدون إطارات"""
        charts_frame = QFrame()
        charts_frame.setStyleSheet("""
            QFrame {
                background: transparent;
                border: none;
            }
        """)

        charts_layout = QVBoxLayout()
        charts_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش لاستغلال الفراغ
        charts_layout.setSpacing(8)  # مسافات مقللة بين المكونات

        # عنوان القسم المتطور مع أيقونة محسنة
        section_title = QLabel("🚀 البيانات التفاعلية والتحليلات الديناميكية")
        section_title.setFont(QFont("Arial", 20, QFont.Bold))
        section_title.setStyleSheet("""
            color: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3B82F6,
                stop:0.5 #8B5CF6,
                stop:1 #EC4899);
            background: transparent;
            padding: 10px;
        """)
        section_title.setAlignment(Qt.AlignCenter)
        charts_layout.addWidget(section_title)

        # رسم بياني شامل متطور
        mega_chart = self.create_mega_chart()
        charts_layout.addWidget(mega_chart)

        charts_frame.setLayout(charts_layout)
        return charts_frame

    def create_mega_chart(self):
        """إنشاء رسم بياني شامل متطور بدون إطارات"""
        chart_frame = QFrame()
        chart_frame.setStyleSheet("""
            QFrame {
                background: transparent;
                border: none;
            }
        """)

        chart_layout = QVBoxLayout()
        chart_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش لاستغلال الفراغ
        chart_layout.setSpacing(6)  # مسافات مقللة بين الصفوف



        # الصف الأول: الإيرادات والمصروفات - مسافات مضبوطة
        financial_row = QHBoxLayout()
        financial_row.setSpacing(10)  # مسافات مضبوطة بين البطاقات

        # بيانات مالية متطورة مع أيقونات وعناوين محسنة
        financial_data = [
            ("💎 الإيرادات الذهبية", "2,450,000", "#27ae60", 85),
            ("🔥 المصروفات الحيوية", "1,680,000", "#e74c3c", 65),
            ("🚀 الأرباح المتسارعة", "770,000", "#3498db", 75),
            ("⚡ الاستثمارات الذكية", "320,000", "#9b59b6", 45)
        ]

        for title, value, color, percentage in financial_data:
            financial_card = self.create_advanced_data_card(title, value, color, percentage)
            financial_row.addWidget(financial_card)

        chart_layout.addLayout(financial_row)

        # الصف الثاني: العملاء والمشاريع - مسافات مضبوطة
        business_row = QHBoxLayout()
        business_row.setSpacing(10)  # مسافات مضبوطة بين البطاقات

        business_data = [
            ("🌟 العملاء المتميزين", "1,247", "#3498db", 92),
            ("🔥 المشاريع النشطة", "89", "#f39c12", 78),
            ("💫 المشاريع المنجزة", "156", "#27ae60", 95),
            ("⚡ المشاريع العاجلة", "12", "#e74c3c", 15)
        ]

        for title, value, color, percentage in business_data:
            business_card = self.create_advanced_data_card(title, value, color, percentage)
            business_row.addWidget(business_card)

        chart_layout.addLayout(business_row)

        # الصف الثالث: بيانات إضافية - مسافات مضبوطة
        additional_row = QHBoxLayout()
        additional_row.setSpacing(10)  # مسافات مضبوطة بين البطاقات

        additional_data = [
            ("🚀 التقارير المتقدمة", "245", "#9b59b6", 88),
            ("💎 العقود الذهبية", "67", "#e67e22", 72),
            ("🌟 الإنجازات المتميزة", "34", "#2ecc71", 95),
            ("⚡ المهام السريعة", "18", "#e74c3c", 25)
        ]

        for title, value, color, percentage in additional_data:
            additional_card = self.create_advanced_data_card(title, value, color, percentage)
            additional_row.addWidget(additional_card)

        chart_layout.addLayout(additional_row)

        chart_frame.setLayout(chart_layout)
        return chart_frame

    def create_advanced_data_card(self, title, value, color, percentage):
        """إنشاء بطاقة بيانات متطورة بدون إطارات مع ارتفاع موحد"""
        card = QFrame()
        card.setFixedHeight(118)  # إنزال بدرجتين (120 - 2 = 118)
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.98),
                    stop:0.12 rgba(248, 250, 252, 0.96),
                    stop:0.25 rgba(241, 245, 249, 0.94),
                    stop:0.38 rgba(226, 232, 240, 0.92),
                    stop:0.5 rgba(203, 213, 225, 0.9),
                    stop:0.62 rgba(148, 163, 184, 0.88),
                    stop:0.75 rgba(100, 116, 139, 0.86),
                    stop:0.88 rgba(71, 85, 105, 0.84),
                    stop:1 rgba(51, 65, 85, 0.82));
                border: none;
                border-radius: 22px;
                margin: 1px;
                box-shadow: 0 12px 30px rgba(15, 23, 42, 0.05),
                           0 8px 20px rgba(37, 99, 235, 0.04),
                           0 4px 12px rgba(59, 130, 246, 0.03),
                           0 2px 6px rgba(96, 165, 250, 0.02),
                           0 1px 3px rgba(147, 197, 253, 0.01),
                           inset 0 2px 4px rgba(255, 255, 255, 0.9),
                           inset 0 -1px 2px rgba(148, 163, 184, 0.06);
                transition: all 0.45s cubic-bezier(0.23, 1, 0.32, 1);
            }}
            QFrame:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 1.0),
                    stop:0.12 rgba(255, 255, 255, 1.0),
                    stop:0.25 rgba(255, 255, 255, 1.0),
                    stop:0.38 rgba(255, 255, 255, 1.0),
                    stop:0.5 rgba(255, 255, 255, 1.0),
                    stop:0.62 rgba(248, 250, 252, 0.98),
                    stop:0.75 rgba(241, 245, 249, 0.96),
                    stop:0.88 rgba(226, 232, 240, 0.94),
                    stop:1 rgba(203, 213, 225, 0.92));
                border: none;
                transform: translateY(-7px) scale(1.03) rotateX(1deg);
                box-shadow: 0 25px 45px rgba(15, 23, 42, 0.12),
                           0 15px 30px rgba(37, 99, 235, 0.1),
                           0 10px 20px rgba(59, 130, 246, 0.08),
                           0 6px 12px rgba(96, 165, 250, 0.06),
                           0 3px 8px rgba(147, 197, 253, 0.04),
                           inset 0 3px 6px rgba(255, 255, 255, 1.0),
                           inset 0 -2px 4px rgba(148, 163, 184, 0.1);
            }}
            QFrame:active {{
                transform: translateY(-3px) scale(1.01);
                transition: all 0.12s cubic-bezier(0.4, 0, 0.6, 1);
            }}
        """)

        card_layout = QVBoxLayout()
        card_layout.setContentsMargins(6, 6, 6, 6)  # هوامش محسنة للمظهر الأنيق
        card_layout.setSpacing(3)  # مسافات متوازنة بين العناصر

        # العنوان مع تصميم متطور ومحسن
        title_label = QLabel(title)
        title_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"""
            color: {color};
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(255, 255, 255, 0.25),
                stop:0.3 rgba(255, 255, 255, 0.18),
                stop:0.6 rgba(255, 255, 255, 0.12),
                stop:1 rgba(255, 255, 255, 0.08));
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(255, 255, 255, 0.9),
                        1px 1px 3px rgba(255, 255, 255, 0.7),
                        0px 0px 2px rgba(0, 0, 0, 0.1);
            letter-spacing: 0.4px;
            border-radius: 10px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06),
                       inset 0 1px 2px rgba(255, 255, 255, 0.8);
        """)
        title_label.setFixedHeight(26)
        title_label.setScaledContents(False)
        title_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        card_layout.addWidget(title_label)

        # القيمة مع تصميم متطور ومحسن
        value_label = QLabel(value)
        value_label.setFont(QFont("Segoe UI", 19, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet("""
            color: #0f172a;
            background: qradialgradient(cx:0.5, cy:0.5, radius:0.8,
                stop:0 rgba(255, 255, 255, 0.35),
                stop:0.3 rgba(255, 255, 255, 0.25),
                stop:0.6 rgba(255, 255, 255, 0.18),
                stop:0.8 rgba(255, 255, 255, 0.12),
                stop:1 rgba(255, 255, 255, 0.08));
            font-weight: bold;
            text-shadow: 3px 3px 6px rgba(255, 255, 255, 0.95),
                        2px 2px 4px rgba(255, 255, 255, 0.8),
                        1px 1px 3px rgba(0, 0, 0, 0.08),
                        0px 0px 2px rgba(255, 255, 255, 0.6);
            letter-spacing: 1px;
            border-radius: 12px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08),
                       inset 0 1px 3px rgba(255, 255, 255, 0.9);
        """)
        value_label.setFixedHeight(28)
        value_label.setScaledContents(False)
        value_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        card_layout.addWidget(value_label)

        # شريط التقدم المتطور والمحسن
        progress = QProgressBar()
        progress.setRange(0, 100)
        progress.setValue(percentage)
        progress.setFixedHeight(12)
        progress.setTextVisible(False)
        progress.setStyleSheet(f"""
            QProgressBar {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(248, 250, 252, 0.98),
                    stop:0.25 rgba(241, 245, 249, 0.94),
                    stop:0.5 rgba(226, 232, 240, 0.9),
                    stop:0.75 rgba(203, 213, 225, 0.86),
                    stop:1 rgba(148, 163, 184, 0.82));
                border: none;
                border-radius: 6px;
                box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.06),
                           inset 0 1px 3px rgba(148, 163, 184, 0.12),
                           0 1px 2px rgba(255, 255, 255, 0.8);
            }}
            QProgressBar::chunk {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {color},
                    stop:0.15 rgba(255, 255, 255, 0.4),
                    stop:0.3 rgba(255, 255, 255, 0.25),
                    stop:0.5 rgba(255, 255, 255, 0.2),
                    stop:0.7 rgba(255, 255, 255, 0.25),
                    stop:0.85 rgba(255, 255, 255, 0.4),
                    stop:1 {color});
                border-radius: 5px;
                box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1),
                           0 2px 4px rgba(0, 0, 0, 0.08),
                           0 1px 2px rgba(0, 0, 0, 0.06),
                           inset 0 2px 4px rgba(255, 255, 255, 0.8),
                           inset 0 -1px 2px rgba(0, 0, 0, 0.06);
            }}
        """)
        card_layout.addWidget(progress)

        # النسبة المئوية مع تصميم متطور ومحسن
        percentage_label = QLabel(f"{percentage}%")
        percentage_label.setFont(QFont("Segoe UI", 10, QFont.Bold))
        percentage_label.setAlignment(Qt.AlignCenter)
        percentage_label.setStyleSheet(f"""
            color: {color};
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(255, 255, 255, 0.5),
                stop:0.3 rgba(255, 255, 255, 0.35),
                stop:0.6 rgba(255, 255, 255, 0.25),
                stop:1 rgba(255, 255, 255, 0.18));
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(255, 255, 255, 0.95),
                        1px 1px 3px rgba(255, 255, 255, 0.8),
                        0px 0px 2px rgba(0, 0, 0, 0.1),
                        0px 0px 1px rgba(255, 255, 255, 0.6);
            letter-spacing: 0.5px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.12),
                       0 1px 3px rgba(0, 0, 0, 0.08),
                       inset 0 2px 4px rgba(255, 255, 255, 0.9),
                       inset 0 -1px 2px rgba(0, 0, 0, 0.05);
        """)
        percentage_label.setFixedHeight(20)
        percentage_label.setScaledContents(False)
        percentage_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        card_layout.addWidget(percentage_label)

        card.setLayout(card_layout)
        return card

    def apply_elegant_styling(self):
        """تطبيق التصميم الأنيق"""
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #475569, stop:0.8 #334155,
                    stop:0.9 #1E293B, stop:1 #0F172A);
                color: #ffffff;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
        """)
