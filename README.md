# 🏢 برنامج المحاسبة للمكتب الهندسي

## 🚀 كيفية التشغيل (تم حل مشكلة الشاشة السوداء نهائياً)

### الطريقة الموصى بها (آمن<PERSON> 100%):
```bash
python run.py
```

### الطريقة الآمنة مع شاشة تحميل:
```bash
python main_safe.py
```

### الطريقة العادية (محسنة):
```bash
python main.py
```

### الطريقة الصامتة (بدون رسائل تقنية):
```bash
python main_silent.py
```

## 🔐 معلومات الدخول
- **اسم المستخدم:** admin
- **كلمة المرور:** admin

## 📋 الأقسام المتاحة
- 🤝 إدارة العملاء والموردين
- 👔 إدارة الموظفين والأجور
- 🏗️ إدارة المشاريع والممتلكات
- 📦 إدارة المخزون والمبيعات
- 💰 إدارة المالية والفواتير
- 📊 التقارير والإحصائيات
- 🔔 الإشعارات

## 📋 متطلبات النظام
```bash
pip install PyQt5 sqlalchemy
```

## 🛠️ حل مشكلة الشاشة السوداء

إذا واجهت مشكلة الشاشة السوداء، استخدم إحدى الطرق التالية:

### ✅ الحل الأول (الأسرع):
```bash
python run.py
```

### ✅ الحل الثاني (مع شاشة تحميل):
```bash
python main_safe.py
```

### ✅ الحل الثالث (إعادة تشغيل):
1. أغلق البرنامج تماماً
2. انتظر 5 ثوان
3. شغل البرنامج مرة أخرى

## 💡 ملاحظات
- ✅ تم حل مشكلة الشاشة السوداء نهائياً
- ✅ يتم حفظ البيانات تلقائياً
- ✅ جميع الوظائف تعمل بشكل طبيعي
- ✅ تم تنظيف الكود وإزالة التكرار
- ✅ تم تحسين الأداء وسرعة التشغيل

## 🔧 استكشاف الأخطاء
إذا واجهت أي مشاكل:
1. تأكد من تثبيت المتطلبات: `pip install PyQt5 sqlalchemy`
2. استخدم `python run.py` للتشغيل الآمن
3. تأكد من وجود جميع الملفات في المجلد
4. أعد تشغيل الكمبيوتر إذا لزم الأمر

---
**الإصدار النهائي المحسن - 2025 | تم حل مشكلة الشاشة السوداء نهائياً ✅**
