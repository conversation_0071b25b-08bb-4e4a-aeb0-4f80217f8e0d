"""
شاشة الدخول المتطورة والآمنة للبرنامج
تتضمن: تشفير متقدم، رسوم متحركة، تصميم عصري، أمان محسن
"""

import sys
import datetime
import json
import os
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QLineEdit, QPushButton, QFrame, QApplication,
                             QGraphicsDropShadowEffect, QCheckBox, QSpacerItem,
                             QSizePolicy, QGridLayout, QMessageBox, QProgressBar,
                             QComboBox, QDesktopWidget)
from PyQt5.QtCore import (Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtSignal,
                          QRect, QThread, pyqtSlot, QParallelAnimationGroup, QSequentialAnimationGroup)
from PyQt5.QtGui import (QFont, QPixmap, QPainter, QBrush, QColor, QPen, QLinearGradient,
                         QRadialGradient, QIcon, QCursor, QPalette)
from database import get_session, User, authenticate_user, hash_password


class LoginAuthThread(QThread):
    """خيط منفصل للمصادقة لتجنب تجميد الواجهة"""
    auth_completed = pyqtSignal(bool, str, object)  # success, message, user

    def __init__(self, username, password):
        super().__init__()
        self.username = username
        self.password = password

    def run(self):
        """تشغيل عملية المصادقة في خيط منفصل"""
        try:
            session = get_session()
            user, message = authenticate_user(session, self.username, self.password)

            if user:
                self.auth_completed.emit(True, message, user)
            else:
                self.auth_completed.emit(False, message, None)
                session.close()
        except Exception as e:
            self.auth_completed.emit(False, f"خطأ في النظام: {str(e)}", None)


class LoginScreen(QWidget):
    """شاشة الدخول المتطورة والآمنة"""

    # إشارات للتواصل مع النافذة الرئيسية
    login_successful = pyqtSignal(object, object)  # session, user

    def __init__(self):
        super().__init__()
        self.session = None
        self.auth_thread = None
        self.login_attempts = 0
        self.max_attempts = 5
        self.lockout_time = 300  # 5 دقائق
        self.last_attempt_time = None
        self.settings_file = "login_settings.json"

        # تحميل الإعدادات المحفوظة
        self.load_settings()

        # إعداد الواجهة والرسوم المتحركة
        self.setup_ui()
        self.setup_animations()
        self.setup_security_features()
        
    def load_settings(self):
        """تحميل إعدادات تسجيل الدخول المحفوظة"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    self.saved_settings = json.load(f)
            else:
                self.saved_settings = {}
        except:
            self.saved_settings = {}

    def save_settings(self):
        """حفظ إعدادات تسجيل الدخول"""
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.saved_settings, f, ensure_ascii=False, indent=2)
        except:
            pass

    def setup_ui(self):
        """إعداد واجهة شاشة الدخول المتطورة"""
        # إعداد النافذة الأساسية
        self.setWindowTitle("🔐 Smart Finish - نظام الدخول الآمن والمتطور")

        # تحديد حجم النافذة بناءً على حجم الشاشة
        desktop = QDesktopWidget()
        screen_rect = desktop.screenGeometry()
        window_width = min(1600, int(screen_rect.width() * 0.8))
        window_height = min(1000, int(screen_rect.height() * 0.8))
        self.setFixedSize(window_width, window_height)

        # إعدادات النافذة المتقدمة
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setWindowIcon(QIcon("🔐"))

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # إنشاء الإطار الرئيسي مع تدرج متطور
        self.main_frame = QFrame()
        self.main_frame.setObjectName("mainFrame")
        self.main_frame.setStyleSheet(self.get_main_frame_style())

        # إضافة تأثيرات بصرية متقدمة
        self.add_visual_effects()

        main_layout.addWidget(self.main_frame)

        # تخطيط الإطار الرئيسي
        frame_layout = QVBoxLayout(self.main_frame)
        frame_layout.setContentsMargins(60, 50, 60, 50)
        frame_layout.setSpacing(35)

        # إنشاء أقسام الواجهة
        self.create_header_section(frame_layout)
        self.create_security_status(frame_layout)
        self.create_login_section(frame_layout)
        self.create_progress_section(frame_layout)
        self.create_buttons_section(frame_layout)
        self.create_footer_section(frame_layout)

        # توسيط النافذة على الشاشة
        self.center_window()
        
    def get_main_frame_style(self):
        """الحصول على نمط الإطار الرئيسي المتطور"""
        return """
            QFrame#mainFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0A0E27, stop:0.1 #1A1B3A, stop:0.2 #2D3561,
                    stop:0.3 #3E4A89, stop:0.4 #1E40AF, stop:0.5 #2563EB,
                    stop:0.6 #3B82F6, stop:0.7 #6366F1, stop:0.8 #8B5CF6,
                    stop:0.9 #A855F7, stop:1 #C084FC);
                border-radius: 30px;
                border: 4px solid rgba(255, 255, 255, 0.15);
            }
        """

    def add_visual_effects(self):
        """إضافة التأثيرات البصرية المتقدمة"""
        # تأثير الظل الرئيسي
        main_shadow = QGraphicsDropShadowEffect()
        main_shadow.setBlurRadius(40)
        main_shadow.setColor(QColor(0, 0, 0, 120))
        main_shadow.setOffset(0, 15)
        self.main_frame.setGraphicsEffect(main_shadow)

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        desktop = QDesktopWidget()
        screen_rect = desktop.screenGeometry()
        x = (screen_rect.width() - self.width()) // 2
        y = (screen_rect.height() - self.height()) // 2
        self.move(x, y)

    def create_header_section(self, parent_layout):
        """إنشاء منطقة العنوان المتطورة"""
        header_frame = QFrame()
        header_frame.setFixedHeight(220)
        header_layout = QVBoxLayout(header_frame)
        header_layout.setAlignment(Qt.AlignCenter)
        header_layout.setSpacing(18)

        # الشعار المتحرك
        self.logo_label = QLabel("🔐")
        self.logo_label.setAlignment(Qt.AlignCenter)
        self.logo_label.setStyleSheet("""
            QLabel {
                font-size: 72px;
                padding: 20px;
                background: qradial-gradient(circle,
                    rgba(255, 255, 255, 0.25) 0%,
                    rgba(59, 130, 246, 0.4) 40%,
                    rgba(139, 92, 246, 0.3) 70%,
                    rgba(168, 85, 247, 0.2) 100%);
                border-radius: 60px;
                border: 3px solid rgba(255, 255, 255, 0.4);
            }
        """)

        # العنوان الرئيسي المتطور
        title_label = QLabel("🏢 Smart Finish")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 52px;
                font-weight: bold;
                font-family: 'Arial', 'Segoe UI', 'Tahoma', sans-serif;
                text-shadow: 0 6px 12px rgba(0, 0, 0, 0.6);
                padding: 25px;
                border-radius: 20px;
            }
        """)

        # العنوان الفرعي المحسن
        subtitle_label = QLabel("🛡️ نظام الدخول الآمن والمتطور")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.95);
                font-size: 26px;
                font-weight: 600;
                font-family: 'Arial', 'Segoe UI', 'Tahoma', sans-serif;
                text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
                padding: 12px;
            }
        """)

        header_layout.addWidget(self.logo_label)
        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)

        parent_layout.addWidget(header_frame)
        
    def create_security_status(self, parent_layout):
        """إنشاء منطقة حالة الأمان"""
        security_frame = QFrame()
        security_frame.setFixedHeight(60)
        security_layout = QHBoxLayout(security_frame)
        security_layout.setContentsMargins(20, 10, 20, 10)

        # مؤشر الأمان
        self.security_status = QLabel("🔒 النظام آمن ومحمي")
        self.security_status.setAlignment(Qt.AlignCenter)
        self.security_status.setStyleSheet("""
            QLabel {
                color: #10B981;
                font-size: 18px;
                font-weight: bold;
                font-family: 'Arial', 'Segoe UI', sans-serif;
                background: rgba(16, 185, 129, 0.1);
                border: 2px solid rgba(16, 185, 129, 0.3);
                border-radius: 15px;
                padding: 8px 15px;
            }
        """)

        # عداد المحاولات
        self.attempts_label = QLabel(f"المحاولات المتبقية: {self.max_attempts - self.login_attempts}")
        self.attempts_label.setAlignment(Qt.AlignCenter)
        self.attempts_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 14px;
                font-weight: 500;
                padding: 5px;
            }
        """)

        security_layout.addWidget(self.security_status)
        security_layout.addWidget(self.attempts_label)
        parent_layout.addWidget(security_frame)

    def create_login_section(self, parent_layout):
        """إنشاء منطقة تسجيل الدخول المتطورة"""
        login_frame = QFrame()
        login_frame.setFixedHeight(350)
        login_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.12),
                    stop:0.5 rgba(255, 255, 255, 0.18),
                    stop:1 rgba(255, 255, 255, 0.12));
                border-radius: 25px;
                border: 3px solid rgba(255, 255, 255, 0.25);
            }
        """)

        # تأثير الظل المحسن
        login_shadow = QGraphicsDropShadowEffect()
        login_shadow.setBlurRadius(25)
        login_shadow.setColor(QColor(0, 0, 0, 60))
        login_shadow.setOffset(0, 8)
        login_frame.setGraphicsEffect(login_shadow)

        login_layout = QGridLayout(login_frame)
        login_layout.setContentsMargins(50, 40, 50, 40)
        login_layout.setSpacing(25)

        # تسمية اسم المستخدم
        username_label = QLabel("👤 اسم المستخدم:")
        username_label.setStyleSheet(self.get_label_style())

        # حقل اسم المستخدم مع القيم المحفوظة
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم...")
        saved_username = self.saved_settings.get('username', 'admin')
        self.username_input.setText(saved_username)
        self.username_input.setStyleSheet(self.get_input_style())
        self.username_input.setFixedHeight(55)

        # تسمية كلمة المرور
        password_label = QLabel("🔒 كلمة المرور:")
        password_label.setStyleSheet(self.get_label_style())

        # حقل كلمة المرور
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور...")
        if self.saved_settings.get('remember_password', False):
            self.password_input.setText("admin")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet(self.get_input_style())
        self.password_input.setFixedHeight(55)

        # زر إظهار/إخفاء كلمة المرور
        self.show_password_btn = QPushButton("👁️")
        self.show_password_btn.setFixedSize(40, 40)
        self.show_password_btn.setStyleSheet(self.get_show_password_style())
        self.show_password_btn.clicked.connect(self.toggle_password_visibility)

        # خانة اختيار "تذكرني"
        self.remember_checkbox = QCheckBox("🔄 تذكر بيانات الدخول")
        self.remember_checkbox.setChecked(self.saved_settings.get('remember_login', True))
        self.remember_checkbox.setStyleSheet(self.get_checkbox_style())

        # ترتيب العناصر في الشبكة
        login_layout.addWidget(username_label, 0, 0)
        login_layout.addWidget(self.username_input, 0, 1, 1, 2)
        login_layout.addWidget(password_label, 1, 0)
        login_layout.addWidget(self.password_input, 1, 1)
        login_layout.addWidget(self.show_password_btn, 1, 2)
        login_layout.addWidget(self.remember_checkbox, 2, 0, 1, 3, Qt.AlignCenter)

        parent_layout.addWidget(login_frame)
        
    def create_progress_section(self, parent_layout):
        """إنشاء منطقة شريط التقدم"""
        progress_frame = QFrame()
        progress_frame.setFixedHeight(80)
        progress_layout = QVBoxLayout(progress_frame)
        progress_layout.setContentsMargins(20, 10, 20, 10)
        progress_layout.setSpacing(10)

        # تسمية حالة العملية
        self.status_label = QLabel("جاهز لتسجيل الدخول")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.9);
                font-size: 16px;
                font-weight: 500;
                font-family: 'Arial', 'Segoe UI', sans-serif;
            }
        """)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 10px;
                background: rgba(255, 255, 255, 0.1);
                text-align: center;
                color: white;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #10B981, stop:0.5 #059669, stop:1 #047857);
                border-radius: 8px;
            }
        """)

        progress_layout.addWidget(self.status_label)
        progress_layout.addWidget(self.progress_bar)
        parent_layout.addWidget(progress_frame)

    def create_buttons_section(self, parent_layout):
        """إنشاء منطقة الأزرار المتطورة"""
        buttons_frame = QFrame()
        buttons_frame.setFixedHeight(140)
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(30, 25, 30, 25)
        buttons_layout.setSpacing(40)

        # زر تسجيل الدخول المحسن
        self.login_button = QPushButton("🚀 تسجيل الدخول الآمن")
        self.login_button.setFixedSize(220, 70)
        self.login_button.setStyleSheet(self.get_primary_button_style())
        self.login_button.clicked.connect(self.handle_login)
        self.login_button.setCursor(QCursor(Qt.PointingHandCursor))

        # زر الإعدادات المتقدمة
        self.settings_button = QPushButton("⚙️ إعدادات")
        self.settings_button.setFixedSize(150, 70)
        self.settings_button.setStyleSheet(self.get_settings_button_style())
        self.settings_button.clicked.connect(self.show_settings)
        self.settings_button.setCursor(QCursor(Qt.PointingHandCursor))

        # زر الإلغاء/الخروج
        self.cancel_button = QPushButton("❌ إلغاء")
        self.cancel_button.setFixedSize(150, 70)
        self.cancel_button.setStyleSheet(self.get_secondary_button_style())
        self.cancel_button.clicked.connect(self.close)
        self.cancel_button.setCursor(QCursor(Qt.PointingHandCursor))

        # ترتيب الأزرار
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.settings_button)
        buttons_layout.addWidget(self.login_button)
        buttons_layout.addStretch()

        parent_layout.addWidget(buttons_frame)

    def create_footer_section(self, parent_layout):
        """إنشاء منطقة التذييل المتطورة"""
        footer_frame = QFrame()
        footer_frame.setFixedHeight(120)
        footer_layout = QVBoxLayout(footer_frame)
        footer_layout.setAlignment(Qt.AlignCenter)
        footer_layout.setSpacing(12)

        # معلومات الإصدار والأمان
        version_label = QLabel("🔐 الإصدار 2.0 - نظام آمن ومشفر")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.85);
                font-size: 16px;
                font-weight: 600;
                font-family: 'Arial', 'Segoe UI', sans-serif;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
            }
        """)

        # معلومات المطور
        developer_label = QLabel("👨‍💻 تطوير: المهندس خالد محسن")
        developer_label.setAlignment(Qt.AlignCenter)
        developer_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.9);
                font-size: 18px;
                font-weight: bold;
                font-family: 'Arial', 'Segoe UI', sans-serif;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
            }
        """)

        # معلومات حقوق الطبع
        copyright_label = QLabel("© 2024 Smart Finish - جميع الحقوق محفوظة")
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.75);
                font-size: 14px;
                font-weight: 500;
                font-family: 'Arial', 'Segoe UI', sans-serif;
            }
        """)

        footer_layout.addWidget(developer_label)
        footer_layout.addWidget(version_label)
        footer_layout.addWidget(copyright_label)

        parent_layout.addWidget(footer_frame)

    def setup_security_features(self):
        """إعداد ميزات الأمان المتقدمة"""
        # تحديد وقت انتهاء الجلسة
        self.session_timeout = QTimer()
        self.session_timeout.timeout.connect(self.handle_session_timeout)

        # مؤقت لتحديث الوقت
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time_display)
        self.time_timer.start(1000)  # تحديث كل ثانية

    def toggle_password_visibility(self):
        """تبديل إظهار/إخفاء كلمة المرور"""
        if self.password_input.echoMode() == QLineEdit.Password:
            self.password_input.setEchoMode(QLineEdit.Normal)
            self.show_password_btn.setText("🙈")
        else:
            self.password_input.setEchoMode(QLineEdit.Password)
            self.show_password_btn.setText("👁️")

    def show_settings(self):
        """عرض نافذة الإعدادات"""
        # يمكن إضافة نافذة إعدادات متقدمة هنا
        self.show_info_message("⚙️ الإعدادات", "ميزة الإعدادات المتقدمة قيد التطوير")

    def update_time_display(self):
        """تحديث عرض الوقت"""
        current_time = datetime.datetime.now().strftime("%H:%M:%S")
        # يمكن إضافة عرض الوقت في مكان مناسب

    def handle_session_timeout(self):
        """معالجة انتهاء مهلة الجلسة"""
        self.show_error_message("⏰ انتهت المهلة", "انتهت مهلة الجلسة، يرجى المحاولة مرة أخرى")
        self.reset_login_form()

    def get_label_style(self):
        """الحصول على نمط التسميات المحسن"""
        return """
            QLabel {
                color: white;
                font-size: 20px;
                font-weight: bold;
                font-family: 'Arial', 'Segoe UI', 'Tahoma', sans-serif;
                text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
                padding: 8px;
            }
        """

    def get_input_style(self):
        """الحصول على نمط حقول الإدخال المتطور"""
        return """
            QLineEdit {
                background: rgba(255, 255, 255, 0.95);
                border: 3px solid rgba(59, 130, 246, 0.6);
                border-radius: 15px;
                padding: 15px 20px;
                font-size: 18px;
                font-weight: 500;
                font-family: 'Arial', 'Segoe UI', 'Tahoma', sans-serif;
                color: #1E293B;
                selection-background-color: #3B82F6;
            }
            QLineEdit:focus {
                border: 3px solid #3B82F6;
                background: rgba(255, 255, 255, 1.0);
                box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);
            }
            QLineEdit:hover {
                border: 3px solid rgba(59, 130, 246, 0.8);
                background: rgba(255, 255, 255, 0.98);
            }
            QLineEdit::placeholder {
                color: rgba(30, 41, 59, 0.6);
                font-style: italic;
            }
        """

    def get_show_password_style(self):
        """نمط زر إظهار كلمة المرور"""
        return """
            QPushButton {
                background: rgba(255, 255, 255, 0.9);
                border: 2px solid rgba(59, 130, 246, 0.6);
                border-radius: 20px;
                font-size: 16px;
                color: #1E293B;
            }
            QPushButton:hover {
                background: rgba(255, 255, 255, 1.0);
                border: 2px solid #3B82F6;
            }
            QPushButton:pressed {
                background: rgba(59, 130, 246, 0.1);
            }
        """

    def get_checkbox_style(self):
        """نمط خانة الاختيار المحسن"""
        return """
            QCheckBox {
                color: white;
                font-size: 18px;
                font-weight: 600;
                font-family: 'Arial', 'Segoe UI', 'Tahoma', sans-serif;
                spacing: 12px;
            }
            QCheckBox::indicator {
                width: 24px;
                height: 24px;
                border-radius: 12px;
                border: 3px solid rgba(255, 255, 255, 0.6);
                background: rgba(255, 255, 255, 0.15);
            }
            QCheckBox::indicator:checked {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #10B981, stop:0.5 #059669, stop:1 #047857);
                border: 3px solid #10B981;
            }
            QCheckBox::indicator:hover {
                border: 3px solid rgba(255, 255, 255, 0.8);
                background: rgba(255, 255, 255, 0.2);
            }
        """

    def get_primary_button_style(self):
        """الحصول على نمط الزر الأساسي المتطور"""
        return """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #10B981, stop:0.3 #059669, stop:0.7 #047857, stop:1 #065F46);
                border: 3px solid #10B981;
                border-radius: 20px;
                color: white;
                font-size: 20px;
                font-weight: bold;
                font-family: 'Arial', 'Segoe UI', 'Tahoma', sans-serif;
                text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #34D399, stop:0.3 #10B981, stop:0.7 #059669, stop:1 #047857);
                border: 3px solid #34D399;
                transform: translateY(-3px);
                box-shadow: 0 8px 16px rgba(16, 185, 129, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #047857, stop:0.3 #065F46, stop:0.7 #064E3B, stop:1 #052E16);
                border: 3px solid #047857;
                transform: translateY(1px);
            }
            QPushButton:disabled {
                background: rgba(107, 114, 128, 0.5);
                border: 3px solid rgba(107, 114, 128, 0.3);
                color: rgba(255, 255, 255, 0.5);
            }
        """

    def get_secondary_button_style(self):
        """الحصول على نمط الزر الثانوي المتطور"""
        return """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #DC2626, stop:0.3 #B91C1C, stop:0.7 #991B1B, stop:1 #7F1D1D);
                border: 3px solid #DC2626;
                border-radius: 20px;
                color: white;
                font-size: 18px;
                font-weight: bold;
                font-family: 'Arial', 'Segoe UI', 'Tahoma', sans-serif;
                text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #F87171, stop:0.3 #EF4444, stop:0.7 #DC2626, stop:1 #B91C1C);
                border: 3px solid #F87171;
                transform: translateY(-3px);
                box-shadow: 0 8px 16px rgba(220, 38, 38, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #991B1B, stop:0.3 #7F1D1D, stop:0.7 #450A0A, stop:1 #1F2937);
                border: 3px solid #991B1B;
                transform: translateY(1px);
            }
        """

    def get_settings_button_style(self):
        """نمط زر الإعدادات"""
        return """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #6366F1, stop:0.3 #4F46E5, stop:0.7 #4338CA, stop:1 #3730A3);
                border: 3px solid #6366F1;
                border-radius: 20px;
                color: white;
                font-size: 18px;
                font-weight: bold;
                font-family: 'Arial', 'Segoe UI', 'Tahoma', sans-serif;
                text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #818CF8, stop:0.3 #6366F1, stop:0.7 #4F46E5, stop:1 #4338CA);
                border: 3px solid #818CF8;
                transform: translateY(-3px);
                box-shadow: 0 8px 16px rgba(99, 102, 241, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4338CA, stop:0.3 #3730A3, stop:0.7 #312E81, stop:1 #1E1B4B);
                border: 3px solid #4338CA;
                transform: translateY(1px);
            }
        """

    def setup_animations(self):
        """إعداد الرسوم المتحركة المتطورة"""
        # رسوم متحركة لظهور النافذة
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(1200)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)

        # رسوم متحركة لحركة النافذة
        self.move_animation = QPropertyAnimation(self, b"geometry")
        self.move_animation.setDuration(1000)
        self.move_animation.setEasingCurve(QEasingCurve.OutBack)

        # رسوم متحركة للشعار
        self.logo_animation = QPropertyAnimation(self.logo_label, b"geometry")
        self.logo_animation.setDuration(800)
        self.logo_animation.setEasingCurve(QEasingCurve.OutBounce)

        # مجموعة الرسوم المتحركة المتوازية
        self.entrance_group = QParallelAnimationGroup()
        self.entrance_group.addAnimation(self.fade_animation)
        self.entrance_group.addAnimation(self.move_animation)

        # بدء الرسوم المتحركة عند الظهور
        QTimer.singleShot(150, self.start_entrance_animation)

    def start_entrance_animation(self):
        """بدء رسوم متحركة متطورة للدخول"""
        # تحديد الموضع النهائي (وسط الشاشة)
        desktop = QDesktopWidget()
        screen_rect = desktop.screenGeometry()
        x = (screen_rect.width() - self.width()) // 2
        y = (screen_rect.height() - self.height()) // 2

        # تحديد الموضع الابتدائي (من أعلى الشاشة)
        start_rect = QRect(x, -self.height(), self.width(), self.height())
        end_rect = QRect(x, y, self.width(), self.height())

        # إعداد الرسوم المتحركة
        self.setGeometry(start_rect)
        self.move_animation.setStartValue(start_rect)
        self.move_animation.setEndValue(end_rect)

        # بدء مجموعة الرسوم المتحركة
        self.entrance_group.start()

        # تأثير نبضة للشعار
        QTimer.singleShot(1000, self.start_logo_pulse)

    def start_logo_pulse(self):
        """بدء تأثير النبضة للشعار"""
        # يمكن إضافة تأثير نبضة للشعار هنا
        pass

    def handle_login(self):
        """معالجة عملية تسجيل الدخول المتطورة والآمنة"""
        # التحقق من حالة القفل
        if self.is_locked():
            remaining_time = self.get_remaining_lockout_time()
            self.show_error_message("🔒 الحساب مقفل",
                                   f"تم قفل النظام لمدة {remaining_time} ثانية بسبب المحاولات المتكررة")
            return

        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        # التحقق من صحة البيانات
        if not username or not password:
            self.show_error_message("⚠️ خطأ في البيانات",
                                   "يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        # التحقق من قوة كلمة المرور
        if len(password) < 3:
            self.show_error_message("⚠️ كلمة مرور ضعيفة",
                                   "كلمة المرور يجب أن تكون 3 أحرف على الأقل")
            return

        # بدء عملية المصادقة
        self.start_authentication(username, password)

    def start_authentication(self, username, password):
        """بدء عملية المصادقة في خيط منفصل"""
        # تعطيل الأزرار أثناء المصادقة
        self.set_ui_enabled(False)

        # إظهار شريط التقدم
        self.show_progress("🔐 جاري التحقق من البيانات...")

        # إنشاء وتشغيل خيط المصادقة
        self.auth_thread = LoginAuthThread(username, password)
        self.auth_thread.auth_completed.connect(self.on_authentication_completed)
        self.auth_thread.start()

    @pyqtSlot(bool, str, object)
    def on_authentication_completed(self, success, message, user):
        """معالجة نتيجة المصادقة"""
        # إخفاء شريط التقدم
        self.hide_progress()

        # إعادة تمكين الواجهة
        self.set_ui_enabled(True)

        if success:
            # نجح تسجيل الدخول
            self.login_attempts = 0  # إعادة تعيين عداد المحاولات
            self.save_login_data(user)
            self.show_success_message("✅ نجح تسجيل الدخول",
                                    f"مرحباً بك {user.full_name}")

            # إرسال إشارة نجاح تسجيل الدخول مع تأخير
            QTimer.singleShot(2000, lambda: self.complete_login(user))
        else:
            # فشل تسجيل الدخول
            self.login_attempts += 1
            self.last_attempt_time = datetime.datetime.now()
            self.update_attempts_display()

            if self.login_attempts >= self.max_attempts:
                self.show_error_message("🔒 تم قفل النظام",
                                       f"تم قفل النظام لمدة {self.lockout_time // 60} دقائق بسبب المحاولات المتكررة")
            else:
                self.show_error_message("❌ خطأ في تسجيل الدخول", message)

    def complete_login(self, user):
        """إكمال عملية تسجيل الدخول"""
        try:
            session = get_session()
            self.login_successful.emit(session, user)
            self.close()
        except Exception as e:
            self.show_error_message("❌ خطأ في النظام", f"حدث خطأ: {str(e)}")

    def is_locked(self):
        """التحقق من حالة القفل"""
        if self.login_attempts < self.max_attempts:
            return False

        if self.last_attempt_time:
            time_passed = (datetime.datetime.now() - self.last_attempt_time).total_seconds()
            return time_passed < self.lockout_time

        return False

    def get_remaining_lockout_time(self):
        """الحصول على الوقت المتبقي للقفل"""
        if self.last_attempt_time:
            time_passed = (datetime.datetime.now() - self.last_attempt_time).total_seconds()
            return max(0, int(self.lockout_time - time_passed))
        return 0

    def update_attempts_display(self):
        """تحديث عرض المحاولات المتبقية"""
        remaining = max(0, self.max_attempts - self.login_attempts)
        self.attempts_label.setText(f"المحاولات المتبقية: {remaining}")

        if remaining <= 2:
            self.attempts_label.setStyleSheet("""
                QLabel {
                    color: #EF4444;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 5px;
                }
            """)

    def save_login_data(self, user):
        """حفظ بيانات تسجيل الدخول"""
        if self.remember_checkbox.isChecked():
            self.saved_settings['username'] = user.username
            self.saved_settings['remember_login'] = True
            self.saved_settings['last_login'] = datetime.datetime.now().isoformat()
            self.save_settings()

    def set_ui_enabled(self, enabled):
        """تمكين/تعطيل عناصر الواجهة"""
        self.username_input.setEnabled(enabled)
        self.password_input.setEnabled(enabled)
        self.login_button.setEnabled(enabled)
        self.remember_checkbox.setEnabled(enabled)

    def show_progress(self, message):
        """إظهار شريط التقدم مع رسالة"""
        self.status_label.setText(message)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # شريط تقدم غير محدد

    def hide_progress(self):
        """إخفاء شريط التقدم"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("جاهز لتسجيل الدخول")

    def reset_login_form(self):
        """إعادة تعيين نموذج تسجيل الدخول"""
        self.password_input.clear()
        self.username_input.setFocus()
        self.set_ui_enabled(True)
        self.hide_progress()

    def show_success_message(self, title, message):
        """عرض رسالة نجاح متطورة"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Information)
        msg_box.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        # تطبيق نمط متطور لرسالة النجاح
        msg_box.setStyleSheet("""
            QMessageBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #10B981, stop:0.3 #059669, stop:0.7 #047857, stop:1 #065F46);
                border-radius: 20px;
                border: 3px solid #34D399;
                min-width: 400px;
                min-height: 200px;
            }
            QMessageBox QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                font-family: 'Arial', 'Segoe UI', sans-serif;
                padding: 15px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }
            QMessageBox QPushButton {
                background: rgba(255, 255, 255, 0.95);
                border: 3px solid #10B981;
                border-radius: 12px;
                color: #047857;
                font-weight: bold;
                font-size: 16px;
                padding: 10px 20px;
                min-width: 100px;
                min-height: 40px;
            }
            QMessageBox QPushButton:hover {
                background: white;
                border: 3px solid #34D399;
                transform: translateY(-2px);
            }
        """)

        msg_box.exec_()

    def show_error_message(self, title, message):
        """عرض رسالة خطأ متطورة"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        # تطبيق نمط متطور لرسالة الخطأ
        msg_box.setStyleSheet("""
            QMessageBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #DC2626, stop:0.3 #B91C1C, stop:0.7 #991B1B, stop:1 #7F1D1D);
                border-radius: 20px;
                border: 3px solid #F87171;
                min-width: 400px;
                min-height: 200px;
            }
            QMessageBox QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                font-family: 'Arial', 'Segoe UI', sans-serif;
                padding: 15px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }
            QMessageBox QPushButton {
                background: rgba(255, 255, 255, 0.95);
                border: 3px solid #DC2626;
                border-radius: 12px;
                color: #991B1B;
                font-weight: bold;
                font-size: 16px;
                padding: 10px 20px;
                min-width: 100px;
                min-height: 40px;
            }
            QMessageBox QPushButton:hover {
                background: white;
                border: 3px solid #F87171;
                transform: translateY(-2px);
            }
        """)

        msg_box.exec_()

    def show_info_message(self, title, message):
        """عرض رسالة معلومات"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Information)
        msg_box.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        # تطبيق نمط متطور لرسالة المعلومات
        msg_box.setStyleSheet("""
            QMessageBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3B82F6, stop:0.3 #2563EB, stop:0.7 #1D4ED8, stop:1 #1E40AF);
                border-radius: 20px;
                border: 3px solid #60A5FA;
                min-width: 400px;
                min-height: 200px;
            }
            QMessageBox QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                font-family: 'Arial', 'Segoe UI', sans-serif;
                padding: 15px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }
            QMessageBox QPushButton {
                background: rgba(255, 255, 255, 0.95);
                border: 3px solid #3B82F6;
                border-radius: 12px;
                color: #1E40AF;
                font-weight: bold;
                font-size: 16px;
                padding: 10px 20px;
                min-width: 100px;
                min-height: 40px;
            }
            QMessageBox QPushButton:hover {
                background: white;
                border: 3px solid #60A5FA;
                transform: translateY(-2px);
            }
        """)

        msg_box.exec_()

    def keyPressEvent(self, event):
        """معالجة ضغط المفاتيح"""
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            self.handle_login()
        elif event.key() == Qt.Key_Escape:
            self.close()
        else:
            super().keyPressEvent(event)

    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""
        if self.session:
            self.session.close()
        event.accept()


# اختبار شاشة الدخول
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # إعداد قاعدة البيانات للاختبار
    from database import init_db
    init_db()

    login_screen = LoginScreen()
    login_screen.show()

    sys.exit(app.exec_())
