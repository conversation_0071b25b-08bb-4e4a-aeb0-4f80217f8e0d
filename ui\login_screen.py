"""
شاشة الدخول المتقدمة والكبيرة للبرنامج
"""

import sys
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QLineEdit, QPushButton, QFrame, QApplication,
                             QGraphicsDropShadowEffect, QCheckBox, QSpacerItem,
                             QSizePolicy, QGridLayout, QMessageBox)
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtSignal, QRect
from PyQt5.QtGui import QFont, QPixmap, QPainter, QBrush, QColor, QPen, QLinearGradient
from database import get_session, User
import hashlib


class LoginScreen(QWidget):
    """شاشة الدخول المتقدمة والكبيرة"""
    
    # إشارة لإرسال بيانات المستخدم عند نجاح تسجيل الدخول
    login_successful = pyqtSignal(object, object)  # session, user
    
    def __init__(self):
        super().__init__()
        self.session = None
        self.setup_ui()
        self.setup_animations()
        
    def setup_ui(self):
        """إعداد واجهة شاشة الدخول المتقدمة"""
        # إعداد النافذة الأساسية
        self.setWindowTitle("🔐 Smart Finish - نظام تسجيل الدخول المتطور")
        self.setFixedSize(1400, 900)  # حجم كبير ومتقدم
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # إنشاء الإطار الرئيسي مع التدرج المتقدم
        self.main_frame = QFrame()
        self.main_frame.setObjectName("mainFrame")
        self.main_frame.setStyleSheet("""
            QFrame#mainFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border-radius: 25px;
                border: 3px solid rgba(255, 255, 255, 0.2);
            }
        """)
        
        # إضافة تأثير الظل للإطار الرئيسي
        shadow_effect = QGraphicsDropShadowEffect()
        shadow_effect.setBlurRadius(30)
        shadow_effect.setColor(QColor(0, 0, 0, 100))
        shadow_effect.setOffset(0, 10)
        self.main_frame.setGraphicsEffect(shadow_effect)
        
        main_layout.addWidget(self.main_frame)
        
        # تخطيط الإطار الرئيسي
        frame_layout = QVBoxLayout(self.main_frame)
        frame_layout.setContentsMargins(50, 40, 50, 40)
        frame_layout.setSpacing(30)
        
        # إنشاء منطقة العنوان
        self.create_header_section(frame_layout)
        
        # إنشاء منطقة تسجيل الدخول
        self.create_login_section(frame_layout)
        
        # إنشاء منطقة الأزرار
        self.create_buttons_section(frame_layout)
        
        # إنشاء منطقة التذييل
        self.create_footer_section(frame_layout)
        
    def create_header_section(self, parent_layout):
        """إنشاء منطقة العنوان المتقدمة"""
        header_frame = QFrame()
        header_frame.setFixedHeight(200)
        header_layout = QVBoxLayout(header_frame)
        header_layout.setAlignment(Qt.AlignCenter)
        header_layout.setSpacing(15)
        
        # العنوان الرئيسي
        title_label = QLabel("🏢 Smart Finish")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 48px;
                font-weight: bold;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.5 rgba(255, 215, 0, 0.8),
                    stop:1 rgba(255, 255, 255, 0.9));
                -webkit-background-clip: text;
                border-radius: 15px;
                padding: 20px;
            }
        """)
        
        # العنوان الفرعي
        subtitle_label = QLabel("نظام المحاسبة الإداري المتطور والشامل")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.9);
                font-size: 24px;
                font-weight: 600;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                padding: 10px;
            }
        """)
        
        # شعار أو أيقونة متقدمة
        logo_label = QLabel("💼")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("""
            QLabel {
                font-size: 64px;
                padding: 15px;
                background: qradial-gradient(circle,
                    rgba(255, 255, 255, 0.2) 0%,
                    rgba(59, 130, 246, 0.3) 50%,
                    rgba(139, 92, 246, 0.2) 100%);
                border-radius: 50px;
                border: 2px solid rgba(255, 255, 255, 0.3);
            }
        """)
        
        header_layout.addWidget(logo_label)
        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        
        parent_layout.addWidget(header_frame)
        
    def create_login_section(self, parent_layout):
        """إنشاء منطقة تسجيل الدخول المتقدمة"""
        login_frame = QFrame()
        login_frame.setFixedHeight(300)
        login_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(255, 255, 255, 0.15),
                    stop:1 rgba(255, 255, 255, 0.1));
                border-radius: 20px;
                border: 2px solid rgba(255, 255, 255, 0.2);
            }
        """)
        
        # إضافة تأثير الظل لإطار تسجيل الدخول
        login_shadow = QGraphicsDropShadowEffect()
        login_shadow.setBlurRadius(20)
        login_shadow.setColor(QColor(0, 0, 0, 50))
        login_shadow.setOffset(0, 5)
        login_frame.setGraphicsEffect(login_shadow)
        
        login_layout = QGridLayout(login_frame)
        login_layout.setContentsMargins(40, 30, 40, 30)
        login_layout.setSpacing(20)
        
        # تسمية اسم المستخدم
        username_label = QLabel("👤 اسم المستخدم:")
        username_label.setStyleSheet(self.get_label_style())
        
        # حقل اسم المستخدم
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم...")
        self.username_input.setText("admin")  # قيمة افتراضية
        self.username_input.setStyleSheet(self.get_input_style())
        self.username_input.setFixedHeight(50)
        
        # تسمية كلمة المرور
        password_label = QLabel("🔒 كلمة المرور:")
        password_label.setStyleSheet(self.get_label_style())
        
        # حقل كلمة المرور
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور...")
        self.password_input.setText("admin")  # قيمة افتراضية
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet(self.get_input_style())
        self.password_input.setFixedHeight(50)
        
        # خانة اختيار "تذكرني"
        self.remember_checkbox = QCheckBox("🔄 تذكر بيانات الدخول")
        self.remember_checkbox.setChecked(True)
        self.remember_checkbox.setStyleSheet("""
            QCheckBox {
                color: white;
                font-size: 16px;
                font-weight: 600;
                font-family: 'Arial', 'Tahoma', sans-serif;
                spacing: 10px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border-radius: 10px;
                border: 2px solid rgba(255, 255, 255, 0.5);
                background: rgba(255, 255, 255, 0.1);
            }
            QCheckBox::indicator:checked {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #10B981, stop:1 #059669);
                border: 2px solid #10B981;
            }
        """)
        
        # ترتيب العناصر في الشبكة
        login_layout.addWidget(username_label, 0, 0)
        login_layout.addWidget(self.username_input, 0, 1)
        login_layout.addWidget(password_label, 1, 0)
        login_layout.addWidget(self.password_input, 1, 1)
        login_layout.addWidget(self.remember_checkbox, 2, 0, 1, 2, Qt.AlignCenter)
        
        parent_layout.addWidget(login_frame)
        
    def create_buttons_section(self, parent_layout):
        """إنشاء منطقة الأزرار المتقدمة"""
        buttons_frame = QFrame()
        buttons_frame.setFixedHeight(120)
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(20, 20, 20, 20)
        buttons_layout.setSpacing(30)
        
        # زر تسجيل الدخول
        self.login_button = QPushButton("🚀 تسجيل الدخول")
        self.login_button.setFixedSize(200, 60)
        self.login_button.setStyleSheet(self.get_primary_button_style())
        self.login_button.clicked.connect(self.handle_login)
        
        # زر الإلغاء/الخروج
        self.cancel_button = QPushButton("❌ إلغاء")
        self.cancel_button.setFixedSize(200, 60)
        self.cancel_button.setStyleSheet(self.get_secondary_button_style())
        self.cancel_button.clicked.connect(self.close)
        
        # إضافة مساحة مرنة
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.login_button)
        buttons_layout.addStretch()
        
        parent_layout.addWidget(buttons_frame)

    def create_footer_section(self, parent_layout):
        """إنشاء منطقة التذييل المتقدمة"""
        footer_frame = QFrame()
        footer_frame.setFixedHeight(100)
        footer_layout = QVBoxLayout(footer_frame)
        footer_layout.setAlignment(Qt.AlignCenter)
        footer_layout.setSpacing(10)

        # معلومات حقوق الطبع
        copyright_label = QLabel("© 2024 Smart Finish - جميع الحقوق محفوظة")
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.7);
                font-size: 14px;
                font-weight: 500;
                font-family: 'Arial', 'Tahoma', sans-serif;
            }
        """)

        # معلومات المطور
        developer_label = QLabel("👨‍💻 تطوير: المهندس خالد محسن")
        developer_label.setAlignment(Qt.AlignCenter)
        developer_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 16px;
                font-weight: 600;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
        """)

        footer_layout.addWidget(developer_label)
        footer_layout.addWidget(copyright_label)

        parent_layout.addWidget(footer_frame)

    def get_label_style(self):
        """الحصول على نمط التسميات"""
        return """
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
                padding: 5px;
            }
        """

    def get_input_style(self):
        """الحصول على نمط حقول الإدخال"""
        return """
            QLineEdit {
                background: rgba(255, 255, 255, 0.9);
                border: 2px solid rgba(59, 130, 246, 0.5);
                border-radius: 12px;
                padding: 12px 15px;
                font-size: 16px;
                font-weight: 500;
                font-family: 'Arial', 'Tahoma', sans-serif;
                color: #1E293B;
                selection-background-color: #3B82F6;
            }
            QLineEdit:focus {
                border: 2px solid #3B82F6;
                background: rgba(255, 255, 255, 1.0);
                box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
            }
            QLineEdit::placeholder {
                color: rgba(30, 41, 59, 0.6);
                font-style: italic;
            }
        """

    def get_primary_button_style(self):
        """الحصول على نمط الزر الأساسي"""
        return """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #10B981, stop:0.5 #059669, stop:1 #047857);
                border: 2px solid #10B981;
                border-radius: 15px;
                color: white;
                font-size: 18px;
                font-weight: bold;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #34D399, stop:0.5 #10B981, stop:1 #059669);
                border: 2px solid #34D399;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #047857, stop:0.5 #065F46, stop:1 #064E3B);
                border: 2px solid #047857;
                transform: translateY(1px);
            }
        """

    def get_secondary_button_style(self):
        """الحصول على نمط الزر الثانوي"""
        return """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #DC2626, stop:0.5 #B91C1C, stop:1 #991B1B);
                border: 2px solid #DC2626;
                border-radius: 15px;
                color: white;
                font-size: 18px;
                font-weight: bold;
                font-family: 'Arial', 'Tahoma', sans-serif;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #F87171, stop:0.5 #EF4444, stop:1 #DC2626);
                border: 2px solid #F87171;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #991B1B, stop:0.5 #7F1D1D, stop:1 #450A0A);
                border: 2px solid #991B1B;
                transform: translateY(1px);
            }
        """

    def setup_animations(self):
        """إعداد الرسوم المتحركة المتقدمة"""
        # رسوم متحركة لظهور النافذة
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(1000)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)

        # رسوم متحركة لحركة النافذة
        self.move_animation = QPropertyAnimation(self, b"geometry")
        self.move_animation.setDuration(800)
        self.move_animation.setEasingCurve(QEasingCurve.OutBack)

        # بدء الرسوم المتحركة عند الظهور
        QTimer.singleShot(100, self.start_entrance_animation)

    def start_entrance_animation(self):
        """بدء رسوم متحركة للدخول"""
        # تحديد الموضع النهائي (وسط الشاشة)
        screen = QApplication.desktop().screenGeometry()
        x = (screen.width() - self.width()) // 2
        y = (screen.height() - self.height()) // 2

        # تحديد الموضع الابتدائي (أعلى الشاشة)
        start_rect = QRect(x, -self.height(), self.width(), self.height())
        end_rect = QRect(x, y, self.width(), self.height())

        # إعداد الرسوم المتحركة
        self.setGeometry(start_rect)
        self.move_animation.setStartValue(start_rect)
        self.move_animation.setEndValue(end_rect)

        # بدء الرسوم المتحركة
        self.fade_animation.start()
        self.move_animation.start()

    def handle_login(self):
        """معالجة عملية تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        # التحقق من صحة البيانات
        if not username or not password:
            self.show_error_message("⚠️ خطأ في البيانات",
                                   "يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        try:
            # إنشاء جلسة قاعدة البيانات
            self.session = get_session()

            # البحث عن المستخدم
            user = self.session.query(User).filter_by(username=username).first()

            if user and user.password == password:
                # نجح تسجيل الدخول
                self.show_success_message("✅ نجح تسجيل الدخول",
                                        f"مرحباً بك {user.full_name}")

                # إرسال إشارة نجاح تسجيل الدخول
                QTimer.singleShot(1500, lambda: self.login_successful.emit(self.session, user))
                QTimer.singleShot(1500, self.close)

            else:
                # فشل تسجيل الدخول
                self.show_error_message("❌ خطأ في تسجيل الدخول",
                                       "اسم المستخدم أو كلمة المرور غير صحيحة")

        except Exception as e:
            self.show_error_message("❌ خطأ في النظام",
                                   f"حدث خطأ أثناء تسجيل الدخول:\n{str(e)}")

    def show_success_message(self, title, message):
        """عرض رسالة نجاح متقدمة"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Information)
        msg_box.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        # تطبيق نمط متقدم لرسالة النجاح
        msg_box.setStyleSheet("""
            QMessageBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #10B981, stop:0.5 #059669, stop:1 #047857);
                border-radius: 15px;
                border: 2px solid #34D399;
            }
            QMessageBox QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 10px;
            }
            QMessageBox QPushButton {
                background: rgba(255, 255, 255, 0.9);
                border: 2px solid #10B981;
                border-radius: 8px;
                color: #047857;
                font-weight: bold;
                padding: 8px 16px;
                min-width: 80px;
            }
            QMessageBox QPushButton:hover {
                background: white;
                border: 2px solid #34D399;
            }
        """)

        msg_box.exec_()

    def show_error_message(self, title, message):
        """عرض رسالة خطأ متقدمة"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)

        # تطبيق نمط متقدم لرسالة الخطأ
        msg_box.setStyleSheet("""
            QMessageBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #DC2626, stop:0.5 #B91C1C, stop:1 #991B1B);
                border-radius: 15px;
                border: 2px solid #F87171;
            }
            QMessageBox QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 10px;
            }
            QMessageBox QPushButton {
                background: rgba(255, 255, 255, 0.9);
                border: 2px solid #DC2626;
                border-radius: 8px;
                color: #991B1B;
                font-weight: bold;
                padding: 8px 16px;
                min-width: 80px;
            }
            QMessageBox QPushButton:hover {
                background: white;
                border: 2px solid #F87171;
            }
        """)

        msg_box.exec_()

    def keyPressEvent(self, event):
        """معالجة ضغط المفاتيح"""
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            self.handle_login()
        elif event.key() == Qt.Key_Escape:
            self.close()
        else:
            super().keyPressEvent(event)

    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""
        if self.session:
            self.session.close()
        event.accept()


# اختبار شاشة الدخول
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # إعداد قاعدة البيانات للاختبار
    from database import init_db
    init_db()

    login_screen = LoginScreen()
    login_screen.show()

    sys.exit(app.exec_())
