"""
وحدة مساعدة للتحديد المتعدد في الجداول
تحتوي على دوال مشتركة لجميع الأقسام
"""

from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QShortcut, QMenu, QAction
from PyQt5.QtGui import QKeySequence


class MultiSelectionMixin:
    """كلاس مساعد للتحديد المتعدد"""
    
    def init_multi_selection(self, table_widget):
        """تهيئة التحديد المتعدد للجدول"""
        try:
            self.selected_items = []
            self.table_widget = table_widget
            
            # تغيير نمط التحديد
            table_widget.setSelectionMode(table_widget.ExtendedSelection)
            
            # إعداد الاختصارات والقائمة السياقية
            self.setup_shortcuts()
            self.setup_context_menu()
            
        except Exception as e:
            print(f"خطأ في تهيئة التحديد المتعدد: {e}")
    
    def setup_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        try:
            QShortcut(QKeySequence.SelectAll, self.table_widget, self.select_all_items)
            QShortcut(QKeySequence("Ctrl+D"), self.table_widget, self.deselect_all_items)
            QShortcut(QKeySequence.Delete, self.table_widget, self.delete_selected_items)
        except Exception as e:
            print(f"خطأ في إعداد الاختصارات: {e}")
    
    def setup_context_menu(self):
        """إعداد القائمة السياقية"""
        try:
            self.table_widget.setContextMenuPolicy(Qt.CustomContextMenu)
            self.table_widget.customContextMenuRequested.connect(self.show_context_menu)
        except Exception as e:
            print(f"خطأ في إعداد القائمة السياقية: {e}")
    
    def update_selected_items(self):
        """تحديث قائمة العناصر المحددة"""
        try:
            if not hasattr(self, 'selected_items'):
                self.selected_items = []
            
            self.selected_items = []
            for item in self.table_widget.selectedItems():
                if item.column() == 0:  # عمود ID
                    id_text = ''.join(filter(str.isdigit, item.text()))
                    if id_text:
                        self.selected_items.append(int(id_text))
        except Exception as e:
            print(f"خطأ في تحديث العناصر المحددة: {e}")
    
    def select_all_items(self):
        """تحديد جميع العناصر"""
        try:
            self.table_widget.selectAll()
            self.update_selected_items()
        except Exception as e:
            print(f"خطأ في تحديد الكل: {e}")
    
    def deselect_all_items(self):
        """إلغاء جميع التحديدات"""
        try:
            self.table_widget.clearSelection()
            self.selected_items = []
        except Exception as e:
            print(f"خطأ في إلغاء التحديد: {e}")
    
    def get_selected_count(self):
        """الحصول على عدد العناصر المحددة"""
        try:
            self.update_selected_items()
            return len(self.selected_items)
        except:
            return 0
    
    def has_selection(self):
        """التحقق من وجود تحديد"""
        return self.get_selected_count() > 0
    
    def has_single_selection(self):
        """التحقق من وجود تحديد واحد فقط"""
        return self.get_selected_count() == 1
    
    def has_multiple_selection(self):
        """التحقق من وجود تحديد متعدد"""
        return self.get_selected_count() > 1
    
    def delete_selected_items(self):
        """حذف العناصر المحددة - يجب تنفيذها في الكلاس الفرعي"""
        pass
    
    def show_context_menu(self, position):
        """عرض القائمة السياقية - يجب تنفيذها في الكلاس الفرعي"""
        pass
    
    def update_button_states_multi(self, button_configs):
        """
        تحديث حالة الأزرار حسب التحديد المتعدد
        button_configs: قائمة من (button_attr, button_text, requires_single)
        """
        try:
            selected_count = self.get_selected_count()
            has_sel = self.has_selection()
            has_single = self.has_single_selection()
            
            for button_attr, button_text, requires_single in button_configs:
                if hasattr(self, button_attr):
                    button = getattr(self, button_attr)
                    
                    # تحديد حالة التفعيل
                    if requires_single:
                        enabled = has_single
                    else:
                        enabled = has_sel
                    
                    button.setEnabled(enabled)
                    
                    # تحديث نص زر الحذف
                    if 'delete' in button_attr.lower() and selected_count > 1:
                        button.setText(f"{button_text} ({selected_count})")
                    else:
                        button.setText(button_text)
                        
        except Exception as e:
            print(f"خطأ في تحديث حالة الأزرار: {e}")
    
    def create_context_menu_actions(self, menu, single_actions, multi_actions):
        """
        إنشاء عناصر القائمة السياقية
        single_actions: قائمة من (text, callback) للتحديد الواحد
        multi_actions: قائمة من (text, callback) للتحديد المتعدد
        """
        try:
            selected_count = self.get_selected_count()
            
            if selected_count <= 1:
                for text, callback in single_actions:
                    menu.addAction(text, callback)
            else:
                for text, callback in multi_actions:
                    if "{count}" in text:
                        text = text.format(count=selected_count)
                    menu.addAction(text, callback)
            
            menu.addSeparator()
            menu.addAction("✅ تحديد الكل", self.select_all_items)
            menu.addAction("❌ إلغاء التحديد", self.deselect_all_items)
            
        except Exception as e:
            print(f"خطأ في إنشاء القائمة السياقية: {e}")
