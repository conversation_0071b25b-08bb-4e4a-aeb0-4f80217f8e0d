#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Migration: إضافة صلاحية إدارة الأقساط
تاريخ الإنشاء: 2024-12-19
الهدف: إضافة عمود can_manage_installments لجدول المستخدمين
"""

import sqlite3
import os
import sys

def add_installments_permission():
    """إضافة صلاحية إدارة الأقساط لجدول المستخدمين"""
    
    # مسار قاعدة البيانات
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'accounting.db')
    
    if not os.path.exists(db_path):
        print("❌ لم يتم العثور على قاعدة البيانات")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 بدء إضافة صلاحية إدارة الأقساط...")
        
        # التحقق من وجود العمود
        cursor.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'can_manage_installments' not in columns:
            # إضافة العمود الجديد
            cursor.execute("""
                ALTER TABLE users 
                ADD COLUMN can_manage_installments BOOLEAN DEFAULT 0
            """)
            print("✅ تم إضافة عمود can_manage_installments")
            
            # تحديث صلاحيات المديرين والمدراء التنفيذيين والمحاسبين
            cursor.execute("""
                UPDATE users 
                SET can_manage_installments = 1 
                WHERE role IN ('admin', 'manager', 'accountant')
            """)
            print("✅ تم تحديث صلاحيات المستخدمين الحاليين")
            
        else:
            print("✅ العمود موجود بالفعل")
        
        # حفظ التغييرات
        conn.commit()
        
        # التحقق من النتيجة
        cursor.execute("SELECT COUNT(*) FROM users WHERE can_manage_installments = 1")
        count = cursor.fetchone()[0]
        print(f"✅ عدد المستخدمين الذين لديهم صلاحية إدارة الأقساط: {count}")
        
        return True
        
    except sqlite3.Error as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False
    finally:
        if conn:
            conn.close()

def main():
    """تشغيل الـ migration"""
    print("🚀 بدء migration إضافة صلاحية إدارة الأقساط...")
    
    success = add_installments_permission()
    
    if success:
        print("🎉 تم تنفيذ migration بنجاح!")
        print("📅 يمكن للمستخدمين المخولين الآن الوصول إلى قسم إدارة الأقساط")
    else:
        print("❌ فشل في تنفيذ migration")
        sys.exit(1)

if __name__ == "__main__":
    main()
