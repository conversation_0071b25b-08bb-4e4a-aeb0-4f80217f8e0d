"""
أدوات مشتركة لتخصيص شريط العنوان
"""

import platform
import ctypes
from ctypes import wintypes


class TitleBarStyler:
    """كلاس مشترك لتطبيق تصميم شريط العنوان"""
    
    @staticmethod
    def apply_advanced_title_bar_styling(window):
        """تطبيق تصميم متطور على شريط العنوان"""
        try:
            if platform.system() == "Windows":
                # الحصول على handle النافذة
                hwnd = int(window.winId())
                
                # تعيين لون شريط العنوان
                DWMWA_CAPTION_COLOR = 35
                color = 0x002A170F  # اللون الأساسي #0F172A بتنسيق BGR
                
                ctypes.windll.dwmapi.DwmSetWindowAttribute(
                    hwnd,
                    DWMWA_CAPTION_COLOR,
                    ctypes.byref(wintypes.DWORD(color)),
                    ctypes.sizeof(wintypes.DWORD)
                )
                
                # تعيين لون النص أبيض
                DWMWA_TEXT_COLOR = 36
                text_color = 0x00FFFFFF  # أبيض بتنسيق BGR
                
                ctypes.windll.dwmapi.DwmSetWindowAttribute(
                    hwnd,
                    DWMWA_TEXT_COLOR,
                    ctypes.byref(wintypes.DWORD(text_color)),
                    ctypes.sizeof(wintypes.DWORD)
                )
                
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم شريط العنوان: {e}")
    
    @staticmethod
    def center_title_text(window, original_title):
        """تحسين وضع النص في منتصف شريط العنوان"""
        try:
            # إضافة مسافات لتوسيط النص بصرياً
            padding_spaces = "    "  # مسافات إضافية للتوسيط
            centered_title = f"{padding_spaces}{original_title}{padding_spaces}"
            
            # تحديث العنوان مع التوسيط
            window.setWindowTitle(centered_title)
            
        except Exception as e:
            print(f"تحذير: فشل في توسيط النص: {e}")
