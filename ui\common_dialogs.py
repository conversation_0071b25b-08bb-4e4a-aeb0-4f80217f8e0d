"""
نوافذ الحوار المشتركة للتطبيق
"""

from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
import ctypes
from ctypes import wintypes


class WarningDialog(QDialog):
    """نافذة تحذير متطورة مشابهة لنوافذ البرنامج"""

    def __init__(self, parent=None, message=""):
        super().__init__(parent)
        self.message = message
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنوافذ البرنامج"""
        self.setWindowTitle("⚠️ تحذير - نظام إدارة متطور وشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 200)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        # التخطيط الرئيسي
        layout = QVBoxLayout()
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # رسالة التحذير
        message_label = QLabel(self.message)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #fbbf24;
                font-size: 16px;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                padding: 20px;
                background: rgba(251, 191, 36, 0.1);
                border: 2px solid #fbbf24;
                border-radius: 10px;
            }
        """)
        layout.addWidget(message_label)

        # زر الإغلاق
        button_layout = QHBoxLayout()
        close_button = QPushButton("✅ موافق")
        close_button.setFixedSize(120, 40)
        close_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #10b981, stop:1 #059669);
                color: white;
                border: 3px solid #065f46;
                border-radius: 20px;
                font-size: 14px;
                font-weight: bold;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #059669, stop:1 #047857);
                transform: scale(1.05);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #047857, stop:1 #065f46);
            }
        """)
        close_button.clicked.connect(self.accept)
        
        button_layout.addStretch()
        button_layout.addWidget(close_button)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        self.setLayout(layout)

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق لنوافذ البرنامج"""
        try:
            # الحصول على معرف النافذة
            hwnd = int(self.winId())
            
            # تعيين لون خلفية شريط العنوان
            DWMWA_CAPTION_COLOR = 35
            caption_color = 0x002D1B69  # لون أزرق داكن بتنسيق BGR
            
            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_CAPTION_COLOR,
                ctypes.byref(wintypes.DWORD(caption_color)),
                ctypes.sizeof(wintypes.DWORD)
            )

            # تعيين لون النص أبيض
            DWMWA_TEXT_COLOR = 36
            text_color = 0x00FFFFFF  # أبيض بتنسيق BGR

            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_TEXT_COLOR,
                ctypes.byref(wintypes.DWORD(text_color)),
                ctypes.sizeof(wintypes.DWORD)
            )

        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم شريط العنوان: {e}")
