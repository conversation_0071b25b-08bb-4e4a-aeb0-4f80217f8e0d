#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Migration: إضافة جداول الأقساط
تاريخ الإنشاء: 2024-12-19
الهدف: إضافة جداول installments و installment_items لإدارة الأقساط
"""

import sqlite3
import os
import sys

def add_installments_tables():
    """إضافة جداول الأقساط إلى قاعدة البيانات"""
    
    # مسار قاعدة البيانات
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'accounting.db')
    
    if not os.path.exists(db_path):
        print("❌ لم يتم العثور على قاعدة البيانات")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 بدء إضافة جداول الأقساط...")
        
        # إنشاء جدول الأقساط
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS installments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                installment_number VARCHAR(50) UNIQUE,
                client_id INTEGER,
                date DATETIME DEFAULT CURRENT_TIMESTAMP,
                due_date DATETIME,
                total_amount FLOAT DEFAULT 0.0,
                paid_amount FLOAT DEFAULT 0.0,
                status VARCHAR(20) DEFAULT 'pending',
                notes TEXT,
                FOREIGN KEY (client_id) REFERENCES clients (id)
            )
        """)
        
        # إنشاء جدول عناصر الأقساط
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS installment_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                installment_id INTEGER,
                description VARCHAR(200) NOT NULL,
                quantity FLOAT DEFAULT 1.0,
                unit_price FLOAT NOT NULL,
                total_price FLOAT NOT NULL,
                FOREIGN KEY (installment_id) REFERENCES installments (id) ON DELETE CASCADE
            )
        """)
        
        # إنشاء الفهارس للأقساط
        indexes_installments = [
            "CREATE INDEX IF NOT EXISTS idx_installment_number ON installments (installment_number)",
            "CREATE INDEX IF NOT EXISTS idx_installment_client_id ON installments (client_id)",
            "CREATE INDEX IF NOT EXISTS idx_installment_date ON installments (date)",
            "CREATE INDEX IF NOT EXISTS idx_installment_due_date ON installments (due_date)",
            "CREATE INDEX IF NOT EXISTS idx_installment_total_amount ON installments (total_amount)",
            "CREATE INDEX IF NOT EXISTS idx_installment_paid_amount ON installments (paid_amount)",
            "CREATE INDEX IF NOT EXISTS idx_installment_status ON installments (status)",
            "CREATE INDEX IF NOT EXISTS idx_installment_client_date ON installments (client_id, date)",
            "CREATE INDEX IF NOT EXISTS idx_installment_status_date ON installments (status, date)",
            "CREATE INDEX IF NOT EXISTS idx_installment_due_status ON installments (due_date, status)",
            "CREATE INDEX IF NOT EXISTS idx_installment_amount_status ON installments (total_amount, status)",
            "CREATE INDEX IF NOT EXISTS idx_installment_client_status ON installments (client_id, status)",
            "CREATE INDEX IF NOT EXISTS idx_installment_date_amount ON installments (date, total_amount)"
        ]
        
        for index_sql in indexes_installments:
            cursor.execute(index_sql)
        
        # إنشاء الفهارس لعناصر الأقساط
        indexes_items = [
            "CREATE INDEX IF NOT EXISTS idx_installment_item_installment_id ON installment_items (installment_id)",
            "CREATE INDEX IF NOT EXISTS idx_installment_item_description ON installment_items (description)",
            "CREATE INDEX IF NOT EXISTS idx_installment_item_quantity ON installment_items (quantity)",
            "CREATE INDEX IF NOT EXISTS idx_installment_item_unit_price ON installment_items (unit_price)",
            "CREATE INDEX IF NOT EXISTS idx_installment_item_total_price ON installment_items (total_price)",
            "CREATE INDEX IF NOT EXISTS idx_installment_item_installment_price ON installment_items (installment_id, total_price)",
            "CREATE INDEX IF NOT EXISTS idx_installment_item_quantity_price ON installment_items (quantity, unit_price)"
        ]
        
        for index_sql in indexes_items:
            cursor.execute(index_sql)
        
        # حفظ التغييرات
        conn.commit()
        
        print("✅ تم إنشاء جداول الأقساط بنجاح")
        print("✅ تم إنشاء جميع الفهارس بنجاح")
        
        # التحقق من إنشاء الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name IN ('installments', 'installment_items')")
        tables = cursor.fetchall()
        
        if len(tables) == 2:
            print("✅ تم التحقق من إنشاء الجداول بنجاح")
            
            # عرض معلومات الجداول
            cursor.execute("PRAGMA table_info(installments)")
            installments_columns = cursor.fetchall()
            print(f"📋 جدول الأقساط يحتوي على {len(installments_columns)} عمود")
            
            cursor.execute("PRAGMA table_info(installment_items)")
            items_columns = cursor.fetchall()
            print(f"📋 جدول عناصر الأقساط يحتوي على {len(items_columns)} عمود")
            
            return True
        else:
            print("❌ فشل في إنشاء الجداول")
            return False
            
    except sqlite3.Error as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False
    finally:
        if conn:
            conn.close()

def main():
    """تشغيل الـ migration"""
    print("🚀 بدء migration إضافة جداول الأقساط...")
    
    success = add_installments_tables()
    
    if success:
        print("🎉 تم تنفيذ migration بنجاح!")
        print("📅 يمكنك الآن استخدام قسم إدارة الأقساط")
    else:
        print("❌ فشل في تنفيذ migration")
        sys.exit(1)

if __name__ == "__main__":
    main()
